import streamlit as st
import streamlit_authenticator as stauth
import yaml
from yaml.loader import SafeLoader
from utils.auth_utils import display_user_info

# 设置页面配置
st.set_page_config(
    page_title="个人工具网站",
    page_icon="🏠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 初始化认证器
def init_authenticator():
    """初始化认证器"""
    with open('./config.yaml') as file:
        config = yaml.load(file, Loader=SafeLoader)

    authenticator = stauth.Authenticate(
        './config.yaml'
    )
    return authenticator, config

# # 保存配置文件
# def save_config(config):
#     """保存配置文件"""
#     with open('./config.yaml', 'w') as file:
#         yaml.dump(config, file, default_flow_style=False, allow_unicode=True)

# 获取认证器和配置
authenticator, config = init_authenticator()

# 将认证器保存到session state中，供其他页面使用
if 'authenticator' not in st.session_state:
    st.session_state.authenticator = authenticator
if 'config' not in st.session_state:
    st.session_state.config = config

# 检查认证状态的辅助函数
def check_authentication():
    """检查用户是否已认证"""
    return st.session_state.get('authentication_status') == True

# 定义页面配置
def get_pages():
    """
    根据认证状态返回可用页面
    这个相当于根据鉴权结果给出可以访问的页面了
    后续可以考虑创建一个新文件或配置文件，用矩阵的方式定义RBAC，而不用在此硬编码
    """
    if check_authentication():
        # 已登录用户可以访问所有页面
        return {
            "主页": [
                st.Page("pages/home.py", title="主页", icon="🏠", default=True)
            ],
            "工具": [
                st.Page("pages/tools/invoice_generator.py", title="发票生成器", icon="🧾"),
                st.Page("pages/tools/calculator.py", title="计算器", icon="🧮"),
                st.Page("pages/tools/text_tools.py", title="文本工具", icon="📝"),
                st.Page("pages/tools/file_manager.py", title="文件管理", icon="📁")
            ],
            "账户": [
                st.Page("pages/auth/profile.py", title="个人资料", icon="👤"),
                st.Page("pages/auth/logout.py", title="登出", icon="👋")
            ]
        }
    else:
        # 未登录用户只能访问登录页面
        return [
            st.Page("pages/auth/login.py", title="登录", icon="🔐", default=True)
        ]

# 在侧边栏显示用户信息
display_user_info()

# 获取页面配置
pages = get_pages()

# 创建导航
pg = st.navigation(pages)

# 运行选中的页面
pg.run()