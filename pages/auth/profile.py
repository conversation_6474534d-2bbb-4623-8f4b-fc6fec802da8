import streamlit as st
from utils.auth_utils import require_authentication, render_hidden_login

# 检查认证
require_authentication()

# 获取认证器和配置
authenticator = st.session_state.get('authenticator')
config = st.session_state.get('config')

def save_config(config):
    """保存配置文件"""
    import yaml
    with open('./config.yaml', 'w') as file:
        yaml.dump(config, file, default_flow_style=False, allow_unicode=True)

# 渲染隐藏的登录组件
render_hidden_login()
save_config(config)

st.title("👤 个人资料")
st.markdown("---")

if st.session_state.get('authentication_status'):
    # 显示用户信息
    st.subheader("用户信息")
    col1, col2 = st.columns(2)
    
    with col1:
        st.write(f"**用户名:** {st.session_state.get('username')}")
        st.write(f"**姓名:** {st.session_state.get('name')}")
    
    with col2:
        # 显示登录状态
        st.success("✅ 已登录")
        if 'login_time' in st.session_state:
            st.write(f"**登录时间:** {st.session_state.get('login_time')}")
    
    st.markdown("---")
    
    # 修改密码
    st.subheader("🔒 修改密码")
    
    with st.expander("点击展开修改密码"):
        try:
            if authenticator.reset_password(st.session_state.get('username')):
                st.success('密码修改成功！')
                save_config(config)
        except Exception as e:
            st.error(f"密码修改错误: {e}")
    
    st.markdown("---")
    
    # 更新用户详情
    st.subheader("📝 更新个人信息")
    
    with st.expander("点击展开更新个人信息"):
        try:
            if authenticator.update_user_details(st.session_state.get('username')):
                st.success('个人信息更新成功！')
                save_config(config)
        except Exception as e:
            st.error(f"信息更新错误: {e}")
    
    st.markdown("---")
    
    # 账户统计
    st.subheader("📊 账户统计")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("登录次数", st.session_state.get('login_count', 1))
    
    with col2:
        st.metric("会话时长", "活跃中")
    
    with col3:
        st.metric("账户状态", "正常")
    
    # 安全设置
    st.markdown("---")
    st.subheader("🛡️ 安全设置")
    
    st.info("🔐 您的账户受到密码保护")
    st.info("🍪 会话将在30天后自动过期")
    
    # 危险操作区域
    st.markdown("---")
    st.subheader("⚠️ 危险操作")
    
    with st.expander("⚠️ 注销账户 (请谨慎操作)", expanded=False):
        st.warning("注意：这将清除您的所有会话数据")
        
        if st.button("🚪 立即登出", type="secondary"):
            # 清除会话状态
            for key in list(st.session_state.keys()):
                if key.startswith('authentication') or key in ['username', 'name']:
                    del st.session_state[key]
            
            st.success("已成功登出")
            st.info("正在跳转到登录页面...")
            st.rerun()

else:
    st.error("请先登录以访问个人资料。")
    st.info("正在跳转到登录页面...")
    st.rerun()
