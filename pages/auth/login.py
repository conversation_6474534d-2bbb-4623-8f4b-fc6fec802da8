import streamlit as st

# 获取认证器
authenticator = st.session_state.get('authenticator')
config = st.session_state.get('config')

# def save_config(config):
#     """保存配置文件"""
#     import yaml
#     with open('./config.yaml', 'w') as file:
#         yaml.dump(config, file, default_flow_style=False, allow_unicode=True)


st.title("🔐 登录")
st.markdown("---")

try:
    authenticator.login(location='main')
except Exception as e:
    st.error(f"登录错误: {e}")

# # 保存配置
# save_config(config)

if st.session_state.get('authentication_status') is False:
    st.error('用户名或密码错误')
elif st.session_state.get('authentication_status') is None:
    st.warning('请输入用户名和密码')
    
    # # 提供注册选项
    # st.markdown("---")
    # st.info("使用我的名字的中文作为 username，我本科大学的全称作为密码即可以游客身份登录！")
elif st.session_state.get('authentication_status'):
    # 登录成功，重新运行应用以更新导航
    st.success(f"欢迎，{st.session_state.get('name')}！")
    st.info("正在跳转到主页...")
    st.rerun()
