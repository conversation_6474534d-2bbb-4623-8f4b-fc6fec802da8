import streamlit as st
from utils.auth_utils import require_authentication

# 检查认证
require_authentication()

st.title("🏠 欢迎来到个人网站")
st.markdown("---")

# 显示欢迎信息
if st.session_state.get('authentication_status'):
    st.success(f"欢迎回来，{st.session_state.get('name')}！")
    
    # 显示功能概览
    st.markdown("## 🛠️ 可用工具")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        ### 🧮 计算器
        - 基础数学运算
        - 科学计算
        - 统计分析
        - 函数图形
        """)
    
    with col2:
        st.markdown("""
        ### 📝 文本工具  
        - 文本分析
        - 格式转换
        - 编码解码
        - 正则表达式
        """)
    
    with col3:
        st.markdown("""
        ### 📁 文件工具
        - 文件管理
        - 格式转换
        - 批量处理
        - 数据分析
        """)
        
    st.markdown("---")
    st.info("💡 请使用左侧导航菜单访问各种工具。")
    
    # 显示最近更新
    st.markdown("## 📈 最近更新")
    st.markdown("""
    - ✅ 添加了多页面导航系统
    - ✅ 集成了用户认证功能
    - ✅ 创建了基础工具页面
    - 🔄 正在开发高级功能...
    """)
else:
    st.warning("请先登录以访问所有功能。")
