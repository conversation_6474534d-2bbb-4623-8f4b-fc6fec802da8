import streamlit as st
from utils.auth_utils import require_authentication, render_hidden_login
from utils.invoice_generator_utils import process_clipboard_data

# 检查认证
require_authentication()

# 获取认证器
authenticator = st.session_state.get('authenticator')

# 渲染隐藏的登录组件
render_hidden_login()

st.title("🧾 发票生成器")
st.markdown("---")

# Insert containers separated into tabs:
tab1, tab2 = st.tabs(["Tab 1", "Tab2"])
tab1.write("this is tab 1")
tab2.write("this is tab 2")

# You can also use "with" notation:
with tab1:
    clipboard_str = st.text_area(
        "请粘贴表格数据区域",
        placeholder="请复制并粘贴包含以下7列数据的区域：\n"
        "客户 | 货品 | 唛头 | 件数 | 立方 | 单价 | 折扣\n"
        "注意：只复制数据行，不要包含表头"
    )
    
    if st.button("解析数据"):
        st.dataframe(data=process_clipboard_data(clipboard_str),
        column_config={
            0: "客户"
        },
        hide_index=True)
