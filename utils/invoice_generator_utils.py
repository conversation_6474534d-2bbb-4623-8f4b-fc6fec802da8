"""
发票生成器相关函数
用于处理发票生成过程中相关的功能
"""

import streamlit as st


# 将复制粘贴的表格数据区域处理并转换成结构化数据
@st.cache_data
def process_clipboard_data(data):
    if not data.strip():
        st.warning("剪贴板中没有文本数据")
        return None
    
    lines = data.strip().split('\n')
    if len(lines) < 1:
        st.warning("剪贴板中没有有效数据")
        return None

    # 解析为二维数组
    parsed_data = []
    for line in lines:
        if not line:
            continue
        
        if '\t' in line:
            row = line.split('\t')
        elif ',' in line:
            row = line.split(',')

        row = [cell.strip() for cell in row]

        # 检查是否每行数据都有7列，如果没有说明复制区域有误
        if len(row) != 7:
            st.warning(f"第{lines.index(line)+1}行数据格式不正确，应为7列")
            continue

        # 处理折扣列（第7列，索引6）的百分号格式
        if len(row) > 6 and row[6]:
            discount_str = row[6].strip()
            if discount_str.endswith('%'):
                try:
                    # 将百分号格式转换为小数
                    discount_value = float(discount_str[:-1]) / 100
                    row[6] = str(discount_value)
                except ValueError:
                    row[6] = '0'
            elif discount_str and discount_str != '0':
                try:
                    # 验证是否为有效数字
                    float(discount_str)
                except ValueError:
                    row[6] = '0'
        
        parsed_data.append(row)
        # 验证数据格式（需要7列：客户、货品、唛头、件数、立方、单价、折扣）
        if len(parsed_data) == 0:
            st.warning("没有解析到有效数据")
            return None

        # st.session_state.parsed_clipboard_invoice_data = parsed_data

    return parsed_data
